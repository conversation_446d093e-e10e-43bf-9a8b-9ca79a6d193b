# Sistema Modbus Master/Slave para Raspberry Pi Pico

Este projeto implementa um sistema que funciona simultaneamente como:
- **Modbus Slave**: Disponibiliza dados de temperatura para outros mestres
- **Modbus Master**: Lê dados de um sensor Modbus externo

## Funcionalidades

- ✅ Slave Modbus RTU no UART 0 com RS485
- ✅ Master Modbus no UART 1 com comunicação serial direta
- ✅ Leitura periódica de sensor externo
- ✅ Display OLED para monitoramento
- ✅ LED indicador de atividade
- ✅ Configuração centralizada
- ✅ Tratamento de erros

## Hardware Necessário

### Conexões do Slave (UART 0 - RS485)
- **TX**: GPIO 0
- **RX**: GPIO 1  
- **DE/RE**: GPIO 15
- **Baudrate**: 9600 bps

### Conexões do Master (UART 1 - Serial)
- **TX**: GPIO 4
- **RX**: GPIO 5
- **Baudrate**: 9600 bps

### Display OLED (I2C)
- **SCL**: GPIO 3
- **SDA**: GPIO 2
- **Resolução**: 72x40 pixels

### LED Indicador
- **LED**: GPIO "LED" (LED onboard)

## Configuração

Edite o arquivo `config.py` para ajustar os parâmetros:

### Configurações do Sensor
```python
SENSOR_SLAVE_ADDR = 1              # Endereço do sensor Modbus
SENSOR_REGISTER_ADDR = 0x0000      # Registro de temperatura no sensor
SENSOR_READ_INTERVAL = 1000        # Intervalo de leitura (ms)
```

### Configurações do Slave
```python
SLAVE_ADDR = 10                    # Nosso endereço como slave
SLAVE_REGISTER_ADDR = 0x10         # Onde disponibilizar a temperatura
```

### Configurações de Comunicação
```python
BAUDRATE = 9600                    # Velocidade de comunicação
DATA_BITS = 8                      # Bits de dados
STOP_BITS = 1                      # Bits de parada
```

## Como Usar

1. **Conecte o hardware** conforme o diagrama de conexões
2. **Configure o sensor** no arquivo `config.py`
3. **Carregue o código** no Raspberry Pi Pico
4. **Monitore via serial** para debug (opcional)

## Funcionamento

1. O sistema inicia como slave no endereço configurado
2. A cada intervalo configurado, lê dados do sensor externo
3. Atualiza o holding register com a temperatura lida
4. Outros mestres podem ler a temperatura no endereço 0x10
5. O display mostra: endereços slave/master e temperatura atual

## Formato dos Dados

- **Temperatura**: Valor inteiro multiplicado por 10 (1 casa decimal)
- **Exemplo**: 235 = 23.5°C
- **Registro**: 0x10 (holding register)

## Monitoramento

### Display OLED
- **Linha 1**: `S:10 M:1` (Slave addr: 10, Master lendo sensor addr: 1)
- **Linha 2**: `23.5C` (Temperatura atual)

### LED Indicador
- **Pisca**: Sistema funcionando normalmente
- **Apagado**: Erro no sistema

### Debug Serial
Mensagens de debug (se `DEBUG_ENABLED = True`):
```
Slave Modbus iniciado - Endereco: 10
Master Modbus configurado para ler sensor no endereco: 1
Temperatura disponivel no endereco 0x10
Temperatura lida do sensor: 23.5C (raw: 235)
Registro atualizado: 0x10 = 23.5C
```

## Tratamento de Erros

- **Erro de comunicação**: Mantém último valor válido
- **Sensor desconectado**: Usa temperatura padrão (23.5°C)
- **Erro geral**: Exibe "ERRO" no display

## Personalização

### Alterar Intervalo de Leitura
```python
SENSOR_READ_INTERVAL = 2000  # 2 segundos
```

### Alterar Endereço do Registro
```python
SLAVE_REGISTER_ADDR = 0x200  # Novo endereço
```

### Desabilitar Debug
```python
DEBUG_ENABLED = False
```

## Compatibilidade

- **Raspberry Pi Pico** com MicroPython
- **Biblioteca uModbus** incluída
- **Sensores Modbus RTU** padrão
- **Mestres Modbus** compatíveis com RTU

## Arquivos do Projeto

- `main.py`: Código principal
- `config.py`: Configurações do sistema
- `ssd1306.py`: Driver do display OLED
- `lib/umodbus/`: Biblioteca Modbus
- `README.md`: Esta documentação
