# Teste rápido para identificar o problema do sensor
# Execute este código para diagnóstico rápido

from lib.umodbus.serial import Serial
import time

def teste_sensor_rapido():
    """
    Teste rápido com as configurações mais comuns
    """
    print("=== TESTE RÁPIDO DO SENSOR ===")
    
    # Configurações para testar
    configs = [
        # [slave_addr, register, baudrate, parity, description]
        [1, 0x0000, 9600, None, "Padrão: addr=1, reg=0x0000"],
        [1, 0x0001, 9600, None, "Registro 0x0001"],
        [1, 0x0002, 9600, None, "Registro 0x0002"],
        [1, 0x0200, 9600, None, "Sua config atual: reg=0x0200"],
        [2, 0x0000, 9600, None, "Slave addr=2"],
        [3, 0x0000, 9600, None, "Slave addr=3"],
        [1, 0x0000, 19200, None, "Baudrate 19200"],
        [1, 0x0000, 9600, 0, "Com paridade par"],
    ]
    
    for slave_addr, register, baudrate, parity, desc in configs:
        print(f"\n--- {desc} ---")
        
        try:
            # Cria master com configuração específica
            master = Serial(
                uart_id=1,
                pins=(4, 5),
                baudrate=baudrate,
                data_bits=8,
                stop_bits=1,
                parity=parity
            )
            
            # Tenta ler holding registers
            print(f"Lendo holding register 0x{register:04X}...", end="")
            result = master.read_holding_registers(
                slave_addr=slave_addr,
                starting_addr=register,
                register_qty=1,
                signed=True
            )
            
            if result and len(result) > 0:
                raw = result[0]
                print(f" RAW: {raw}")
                
                if raw != 0:
                    print(f"  ✅ VALOR ENCONTRADO!")
                    print(f"     /1:   {raw/1:.1f}°C")
                    print(f"     /10:  {raw/10:.1f}°C") 
                    print(f"     /100: {raw/100:.1f}°C")
                    
                    # Verifica se alguma escala dá ~24.8°C
                    for scale in [1, 10, 100]:
                        temp = raw / scale
                        if 24.0 <= temp <= 25.5:
                            print(f"  🎯 TEMPERATURA CORRETA: {temp:.1f}°C (escala /{scale})")
                else:
                    print(f"  ⚠️  Valor zero")
            else:
                print(f" ❌ Sem resposta")
                
        except Exception as e:
            print(f" ❌ Erro: {e}")
        
        # Tenta também input registers
        try:
            print(f"Lendo input register 0x{register:04X}...", end="")
            result = master.read_input_registers(
                slave_addr=slave_addr,
                starting_addr=register,
                register_qty=1,
                signed=True
            )
            
            if result and len(result) > 0:
                raw = result[0]
                print(f" RAW: {raw}")
                
                if raw != 0:
                    print(f"  ✅ INPUT REG ENCONTRADO!")
                    for scale in [1, 10, 100]:
                        temp = raw / scale
                        if 24.0 <= temp <= 25.5:
                            print(f"  🎯 TEMPERATURA CORRETA: {temp:.1f}°C (input reg, escala /{scale})")
            else:
                print(f" ❌ Sem resposta")
                
        except Exception as e:
            print(f" ❌ Erro: {e}")
        
        time.sleep(0.5)

def teste_comunicacao_basica():
    """
    Teste básico de comunicação
    """
    print("\n=== TESTE DE COMUNICAÇÃO BÁSICA ===")
    
    master = Serial(
        uart_id=1,
        pins=(4, 5),
        baudrate=9600,
        data_bits=8,
        stop_bits=1,
        parity=None
    )
    
    # Testa vários endereços de slave
    for addr in range(1, 11):
        try:
            print(f"Testando slave {addr}...", end="")
            result = master.read_holding_registers(
                slave_addr=addr,
                starting_addr=0x0000,
                register_qty=1,
                signed=True
            )
            
            if result:
                print(f" ✅ Resposta: {result[0]}")
            else:
                print(f" ❌ Sem resposta")
                
        except Exception as e:
            print(f" ❌ Erro: {type(e).__name__}")
        
        time.sleep(0.2)

def main():
    print("DIAGNÓSTICO RÁPIDO - SENSOR MODBUS")
    print("=" * 40)
    print("Sensor marca: 24.8°C")
    print("Problema: Recebendo 0 na leitura")
    print("=" * 40)
    
    teste_comunicacao_basica()
    teste_sensor_rapido()
    
    print("\n" + "=" * 40)
    print("PRÓXIMOS PASSOS:")
    print("1. Se encontrou valores ≠ 0, ajuste config.py")
    print("2. Se todos valores = 0, verifique:")
    print("   - Conexão física dos fios")
    print("   - Alimentação do sensor") 
    print("   - Endereço do sensor")
    print("   - Manual do sensor")

if __name__ == "__main__":
    main()
