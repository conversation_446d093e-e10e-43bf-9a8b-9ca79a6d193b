from machine import Pin, I2C, UART
from ssd1306 import SSD1306_I2C
from lib.umodbus.serial import Serial as ModbusRTUMaster
import time

# Configura o I2C para o display OLED
i2c = I2C(1, scl=Pin(3), sda=Pin(2))
oled = SSD1306_I2C(72, 40, i2c)

# UART para LoRa
lora = UART(1, baudrate=9600, tx=Pin(8), rx=Pin(9))

# Pino DE/RE do RS-485 em modo recepção (=0)
de_re = Pin(15, Pin.OUT)
de_re.value(0)

led = Pin("LED", Pin.OUT)

# Configura o mestre Modbus
host = ModbusRTUMaster(
    pins=(0, 1),
    baudrate=9600,
    data_bits=8,
    stop_bits=1,
    parity=None,
    ctrl_pin=de_re,
    uart_id=0
)

# Configurações
slave_addr       = 1
register_address = 512
register_qty     = 1
machine          = 1  # ID único da máquina

while True:
    try:
        # Lê registrador Modbus
        regs = host.read_holding_registers(
            slave_addr,
            register_address,
            register_qty,
            signed=True
        )
        raw = regs[0]
        temperatura_c = raw / 10

        # Exibe no OLED
        oled.fill(0)
        oled.draw_text_scaled("Addr:{}".format(machine), 4, 4, scale=1)
        oled.draw_text_scaled("{:.1f}".format(temperatura_c), 4, 26, scale=2)
        oled.show()

        # Prepara pacote para LoRa
        mensagem = "<ADDR={};TEMP={:.1f}>\n".format(machine, temperatura_c)
        print(mensagem)
        lora.write(mensagem)

        # Pisca LED
        led.on()
        time.sleep(0.1)
        led.off()

        time.sleep(1)

    except Exception as e:
        print("Erro na leitura Modbus:", e)
        oled.fill(0)
        oled.text("000", 0, 0)
        oled.show()


