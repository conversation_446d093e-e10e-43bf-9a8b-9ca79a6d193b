from machine import Pin, I2C
from ssd1306 import SSD1306_I2C
from lib.umodbus.serial import ModbusRTU
import time

# Configura o I2C para o display OLED
i2c = I2C(1, scl=Pin(3), sda=Pin(2))
oled = SSD1306_I2C(72, 40, i2c)

# Pino DE/RE do RS-485
de_re = Pin(15, Pin.OUT)

led = Pin("LED", Pin.OUT)

# Configura o slave Modbus
slave_addr = 10

slave = ModbusRTU(
    addr=slave_addr,
    pins=(0, 1),
    baudrate=9600,
    data_bits=8,
    stop_bits=1,
    parity=None,
    ctrl_pin=15,  # Número do pino, não objeto Pin
    uart_id=0
)

# Configurações
temperatura_celsius = 23.5
register_address = 0x200  # Endereço 0x200 para temperatura
machine = 1  # ID único da máquina

# Converte temperatura para valor inteiro (multiplicado por 10 para manter 1 casa decimal)
temperatura_raw = int(temperatura_celsius * 10)

# Adiciona o holding register no endereço 0x200 com o valor da temperatura
slave.add_hreg(register_address, temperatura_raw)

print("Slave Modbus iniciado - Endereco:", slave_addr)
print("Temperatura disponivel no endereco 0x{:X} = {:.1f}C".format(register_address, temperatura_celsius))

while True:
    try:
        # Processa requisições Modbus
        slave.process()

        # Atualiza o display OLED
        oled.fill(0)
        oled.draw_text_scaled("Slave:{}".format(slave_addr), 4, 4, scale=1)
        oled.draw_text_scaled("{:.1f}C".format(temperatura_celsius), 4, 26, scale=2)
        oled.show()

        # Pisca LED para indicar atividade
        led.on()
        time.sleep(0.05)
        led.off()
        time.sleep(0.05)

    except Exception as e:
        print("Erro no slave Modbus:", e)
        oled.fill(0)
        oled.text("ERRO", 0, 0)
        oled.show()
        time.sleep(1)


