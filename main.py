from machine import Pin, I2C
from ssd1306 import SSD1306_I2C
from lib.umodbus.serial import ModbusRTU, Serial
import time
import config

# Configura o I2C para o display OLED
i2c = I2C(config.OLED_I2C_ID, scl=Pin(config.OLED_SCL_PIN), sda=Pin(config.OLED_SDA_PIN))
oled = SSD1306_I2C(config.OLED_WIDTH, config.OLED_HEIGHT, i2c)

# Pino DE/RE do RS-485
de_re = Pin(config.SLAVE_CTRL_PIN, Pin.OUT)

led = Pin("LED", Pin.OUT)

# Configura o slave Modbus (UART 0 com RS485)
slave = ModbusRTU(
    addr=config.SLAVE_ADDR,
    pins=config.SLAVE_PINS,
    baudrate=config.BAUDRATE,
    data_bits=config.DATA_BITS,
    stop_bits=config.STOP_BITS,
    parity=config.PARITY,
    ctrl_pin=config.SLAVE_CTRL_PIN,
    uart_id=config.SLAVE_UART_ID
)

# Configura o master Modbus (UART 1 para comunicação serial direta)
master = Serial(
    uart_id=config.MASTER_UART_ID,
    pins=config.MASTER_PINS,
    baudrate=config.BAUDRATE,
    data_bits=config.DATA_BITS,
    stop_bits=config.STOP_BITS,
    parity=config.PARITY
    # ctrl_pin não especificado para comunicação serial direta
)

# Variáveis para armazenar dados
temperatura_celsius = config.DEFAULT_TEMP  # Valor padrão
temperatura_raw = int(temperatura_celsius * config.TEMP_SCALE_FACTOR)
last_sensor_read = 0  # Timestamp da última leitura do sensor

# Adiciona o holding register no endereço configurado com o valor da temperatura
slave.add_hreg(config.SLAVE_REGISTER_ADDR, temperatura_raw)

def read_sensor_temperature():
    """
    Lê a temperatura do sensor Modbus externo
    Retorna a temperatura em Celsius ou None se houver erro
    """
    try:
        if config.DEBUG_ENABLED:
            print("Tentando ler sensor - Addr: {}, Reg: 0x{:04X}".format(
                config.SENSOR_SLAVE_ADDR, config.SENSOR_REGISTER_ADDR))

        # Lê 1 holding register do sensor externo
        result = master.read_holding_registers(
            slave_addr=config.SENSOR_SLAVE_ADDR,
            starting_addr=config.SENSOR_REGISTER_ADDR,
            register_qty=1,
            signed=True
        )

        if config.DEBUG_ENABLED:
            print("Resposta do sensor: {}".format(result))

        if result and len(result) > 0:
            # Converte o valor lido para temperatura em Celsius
            temp_raw = result[0]

            # Testa diferentes interpretações do valor
            if config.DEBUG_ENABLED:
                print("Valor raw: {} (0x{:04X})".format(temp_raw, temp_raw & 0xFFFF))
                print("Interpretações possíveis:")
                print("  /1:   {:.1f}C".format(temp_raw / 1))
                print("  /10:  {:.1f}C".format(temp_raw / 10))
                print("  /100: {:.1f}C".format(temp_raw / 100))

            # Usa o fator de escala configurado
            temp_celsius = temp_raw / config.TEMP_SCALE_FACTOR

            if config.DEBUG_ENABLED:
                print("Temperatura lida do sensor: {:.1f}C (raw: {}, escala: {})".format(
                    temp_celsius, temp_raw, config.TEMP_SCALE_FACTOR))

            return temp_celsius
        else:
            if config.DEBUG_ENABLED:
                print("Erro: resposta vazia do sensor")
            return None

    except Exception as e:
        if config.DEBUG_ENABLED:
            print("Erro ao ler sensor:", e)
            print("Tipo do erro:", type(e).__name__)
        return None

print("Slave Modbus iniciado - Endereco:", config.SLAVE_ADDR)
print("Master Modbus configurado para ler sensor no endereco:", config.SENSOR_SLAVE_ADDR)
print("Temperatura disponivel no endereco 0x{:X}".format(config.SLAVE_REGISTER_ADDR))

while True:
    try:
        # Verifica se é hora de ler o sensor
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, last_sensor_read) >= config.SENSOR_READ_INTERVAL:
            # Lê temperatura do sensor externo
            sensor_temp = read_sensor_temperature()

            if sensor_temp is not None:
                temperatura_celsius = sensor_temp
                temperatura_raw = int(temperatura_celsius * config.TEMP_SCALE_FACTOR)

                # Atualiza o holding register do slave com o novo valor
                slave.set_hreg(config.SLAVE_REGISTER_ADDR, temperatura_raw)
                if config.DEBUG_ENABLED:
                    print("Registro atualizado: 0x{:X} = {:.1f}C".format(config.SLAVE_REGISTER_ADDR, temperatura_celsius))

            last_sensor_read = current_time

        # Processa requisições Modbus do slave
        slave.process()

        # Atualiza o display OLED
        oled.fill(0)
        oled.draw_text_scaled("S:{} M:{}".format(config.SLAVE_ADDR, config.SENSOR_SLAVE_ADDR), 4, 4, scale=1)
        oled.draw_text_scaled("{:.1f}C".format(temperatura_celsius), 4, 26, scale=2)
        oled.show()

        # Pisca LED para indicar atividade
        led.on()
        time.sleep(config.LED_BLINK_TIME)
        led.off()
        time.sleep(config.LED_BLINK_TIME)

    except Exception as e:
        print("Erro no loop principal:", e)
        oled.fill(0)
        oled.text("ERRO", 0, 0)
        oled.show()
        time.sleep(1)


