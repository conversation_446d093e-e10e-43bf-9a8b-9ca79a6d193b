# Configurações do sistema Modbus Master/Slave

# === CONFIGURAÇÕES DO SLAVE ===
SLAVE_ADDR = 10                    # Endereço do nosso dispositivo como slave
SLAVE_UART_ID = 0                  # UART 0 para o slave
SLAVE_PINS = (0, 1)                # TX=0, RX=1 para UART 0
SLAVE_CTRL_PIN = 15                # Pino DE/RE para RS485
SLAVE_REGISTER_ADDR = 0x10         # Endereço onde disponibilizar a temperatura

# === CONFIGURAÇÕES DO MASTER ===
MASTER_UART_ID = 1                 # UART 1 para o master
MASTER_PINS = (4, 5)               # TX=4, RX=5 para UART 1
SENSOR_SLAVE_ADDR = 1              # Endereço do sensor Modbus externo
SENSOR_REGISTER_ADDR = 0x0200      # Endereço do registro de temperatura no sensor
SENSOR_READ_INTERVAL = 1000        # Intervalo de leitura em ms (1 segundo)

# === CONFIGURAÇÕES DE COMUNICAÇÃO ===
BAUDRATE = 9600                    # Velocidade de comunicação
DATA_BITS = 8                      # Bits de dados
STOP_BITS = 1                      # Bits de parada
PARITY = None                      # Paridade (None, 0=even, 1=odd)

# === CONFIGURAÇÕES DO DISPLAY ===
OLED_I2C_ID = 1                    # ID do I2C para o display
OLED_SCL_PIN = 3                   # Pino SCL do I2C
OLED_SDA_PIN = 2                   # Pino SDA do I2C
OLED_WIDTH = 72                    # Largura do display
OLED_HEIGHT = 40                   # Altura do display

# === CONFIGURAÇÕES DO SENSOR ===
TEMP_SCALE_FACTOR = 10             # Fator de escala da temperatura (temp * 10)
DEFAULT_TEMP = 23.5                # Temperatura padrão em caso de erro

# === CONFIGURAÇÕES DE DEBUG ===
DEBUG_ENABLED = True               # Habilita mensagens de debug
LED_BLINK_TIME = 0.05              # Tempo de piscar do LED em segundos
