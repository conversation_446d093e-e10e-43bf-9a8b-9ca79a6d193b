# Diagnóstico do sensor Modbus
# Use este código para identificar problemas de comunicação

from lib.umodbus.serial import Serial
import time
import config

def test_sensor_communication():
    """
    Testa diferentes configurações para encontrar o sensor
    """
    print("=== DIAGNÓSTICO DO SENSOR MODBUS ===")
    
    # Configura o master para diagnóstico
    master = Serial(
        uart_id=config.MASTER_UART_ID,
        pins=config.MASTER_PINS,
        baudrate=config.BAUDRATE,
        data_bits=config.DATA_BITS,
        stop_bits=config.STOP_BITS,
        parity=config.PARITY
    )
    
    # Lista de endereços comuns para testar
    test_addresses = [1, 2, 3, 4, 5, 10, 247]
    
    # Lista de registros comuns para temperatura
    test_registers = [
        0x0000,  # Registro 0
        0x0001,  # Registro 1
        0x0002,  # Registro 2
        0x0010,  # Registro 16
        0x0100,  # Registro 256
        0x0200,  # Registro 512 (configuração atual)
        0x1000,  # Registro 4096
        0x3000,  # Input registers
        0x4000   # Input registers
    ]
    
    print("Testando endereços de slave...")
    
    for slave_addr in test_addresses:
        print(f"\n--- Testando slave endereço {slave_addr} ---")
        
        for reg_addr in test_registers:
            try:
                print(f"  Testando registro 0x{reg_addr:04X}...", end="")
                
                # Tenta ler holding registers
                result = master.read_holding_registers(
                    slave_addr=slave_addr,
                    starting_addr=reg_addr,
                    register_qty=1,
                    signed=True
                )
                
                if result and len(result) > 0:
                    raw_value = result[0]
                    temp_celsius = raw_value / config.TEMP_SCALE_FACTOR
                    print(f" ✅ SUCESSO! Valor: {raw_value} ({temp_celsius:.1f}°C)")
                    
                    # Se encontrou um valor razoável para temperatura
                    if 0 < raw_value < 1000:
                        print(f"  >>> POSSÍVEL TEMPERATURA ENCONTRADA! <<<")
                        print(f"      Slave: {slave_addr}, Registro: 0x{reg_addr:04X}")
                        print(f"      Valor raw: {raw_value}, Temperatura: {temp_celsius:.1f}°C")
                else:
                    print(" ❌ Resposta vazia")
                    
            except Exception as e:
                print(f" ❌ Erro: {e}")
            
            # Pequena pausa entre tentativas
            time.sleep(0.1)

def test_input_registers():
    """
    Testa input registers (função 04) ao invés de holding registers (função 03)
    """
    print("\n=== TESTANDO INPUT REGISTERS ===")
    
    master = Serial(
        uart_id=config.MASTER_UART_ID,
        pins=config.MASTER_PINS,
        baudrate=config.BAUDRATE,
        data_bits=config.DATA_BITS,
        stop_bits=config.STOP_BITS,
        parity=config.PARITY
    )
    
    test_addresses = [1, 2, 3, 4, 5]
    test_registers = [0x0000, 0x0001, 0x0002, 0x0010, 0x0100, 0x0200]
    
    for slave_addr in test_addresses:
        print(f"\n--- Testando INPUT REGISTERS slave {slave_addr} ---")
        
        for reg_addr in test_registers:
            try:
                print(f"  Input reg 0x{reg_addr:04X}...", end="")
                
                # Tenta ler input registers
                result = master.read_input_registers(
                    slave_addr=slave_addr,
                    starting_addr=reg_addr,
                    register_qty=1,
                    signed=True
                )
                
                if result and len(result) > 0:
                    raw_value = result[0]
                    temp_celsius = raw_value / config.TEMP_SCALE_FACTOR
                    print(f" ✅ SUCESSO! Valor: {raw_value} ({temp_celsius:.1f}°C)")
                    
                    if 0 < raw_value < 1000:
                        print(f"  >>> POSSÍVEL TEMPERATURA EM INPUT REGISTER! <<<")
                else:
                    print(" ❌ Resposta vazia")
                    
            except Exception as e:
                print(f" ❌ Erro: {e}")
            
            time.sleep(0.1)

def test_different_scales():
    """
    Testa diferentes fatores de escala para o valor lido
    """
    print("\n=== TESTANDO DIFERENTES ESCALAS ===")
    
    # Simula um valor lido do sensor (substitua pelo valor real que você está recebendo)
    raw_value = 248  # Exemplo: se o sensor retorna 248 para 24.8°C
    
    print(f"Valor raw recebido: {raw_value}")
    print("Testando diferentes interpretações:")
    
    scales = [1, 10, 100, 0.1, 0.01]
    
    for scale in scales:
        temp = raw_value / scale
        print(f"  Escala {scale}: {temp:.2f}°C")
        
        # Verifica se está próximo da temperatura esperada (24.8°C)
        if 24.0 <= temp <= 25.0:
            print(f"    >>> ESCALA CORRETA ENCONTRADA: {scale} <<<")

def test_communication_settings():
    """
    Testa diferentes configurações de comunicação
    """
    print("\n=== TESTANDO CONFIGURAÇÕES DE COMUNICAÇÃO ===")
    
    baudrates = [9600, 19200, 38400, 115200]
    parities = [None, 0, 1]  # None, Even, Odd
    
    for baudrate in baudrates:
        for parity in parities:
            parity_name = "None" if parity is None else ("Even" if parity == 0 else "Odd")
            print(f"\nTestando: {baudrate} bps, Paridade: {parity_name}")
            
            try:
                master = Serial(
                    uart_id=config.MASTER_UART_ID,
                    pins=config.MASTER_PINS,
                    baudrate=baudrate,
                    data_bits=config.DATA_BITS,
                    stop_bits=config.STOP_BITS,
                    parity=parity
                )
                
                # Testa com configuração atual
                result = master.read_holding_registers(
                    slave_addr=config.SENSOR_SLAVE_ADDR,
                    starting_addr=config.SENSOR_REGISTER_ADDR,
                    register_qty=1,
                    signed=True
                )
                
                if result and len(result) > 0 and result[0] != 0:
                    print(f"  ✅ SUCESSO! Valor: {result[0]}")
                    print(f"    >>> CONFIGURAÇÃO CORRETA ENCONTRADA! <<<")
                    return
                else:
                    print(f"  ❌ Sem resposta ou valor zero")
                    
            except Exception as e:
                print(f"  ❌ Erro: {e}")
            
            time.sleep(0.5)

def main():
    """
    Executa todos os diagnósticos
    """
    print("DIAGNÓSTICO COMPLETO DO SENSOR MODBUS")
    print("=" * 50)
    
    print("Configuração atual:")
    print(f"- Slave addr: {config.SENSOR_SLAVE_ADDR}")
    print(f"- Registro: 0x{config.SENSOR_REGISTER_ADDR:04X}")
    print(f"- Baudrate: {config.BAUDRATE}")
    print(f"- Pinos: {config.MASTER_PINS}")
    
    # Executa os testes
    test_sensor_communication()
    test_input_registers()
    test_different_scales()
    test_communication_settings()
    
    print("\n" + "=" * 50)
    print("DIAGNÓSTICO CONCLUÍDO")
    print("Verifique os resultados acima para encontrar a configuração correta.")

if __name__ == "__main__":
    main()
