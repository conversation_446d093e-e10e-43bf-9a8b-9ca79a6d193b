# Teste do sistema Modbus Master/Slave
# Este arquivo pode ser usado para testar a comunicação

from lib.umodbus.serial import Serial
import time
import config

def test_as_master():
    """
    Testa o sistema funcionando como master
    Lê dados do nosso próprio slave para verificar se está funcionando
    """
    print("=== TESTE COMO MASTER ===")
    
    # Cria um master para testar nosso slave
    test_master = Serial(
        uart_id=1,  # Usar UART diferente para teste
        pins=(4, 5),
        baudrate=config.BAUDRATE,
        data_bits=config.DATA_BITS,
        stop_bits=config.STOP_BITS,
        parity=config.PARITY
    )
    
    try:
        # Tenta ler do nosso slave
        result = test_master.read_holding_registers(
            slave_addr=config.SLAVE_ADDR,
            starting_addr=config.SLAVE_REGISTER_ADDR,
            register_qty=1,
            signed=True
        )
        
        if result and len(result) > 0:
            temp_raw = result[0]
            temp_celsius = temp_raw / config.TEMP_SCALE_FACTOR
            print("✅ Leitura bem-sucedida!")
            print("   Temperatura: {:.1f}C (raw: {})".format(temp_celsius, temp_raw))
            print("   Endereço: 0x{:X}".format(config.SLAVE_REGISTER_ADDR))
        else:
            print("❌ Erro: Resposta vazia")
            
    except Exception as e:
        print("❌ Erro na comunicação:", e)

def test_sensor_simulation():
    """
    Simula um sensor Modbus para testar o master
    """
    print("\n=== SIMULAÇÃO DE SENSOR ===")
    print("Para testar completamente, conecte um sensor real no endereço", config.SENSOR_SLAVE_ADDR)
    print("Ou use um simulador Modbus configurado com:")
    print("- Endereço slave:", config.SENSOR_SLAVE_ADDR)
    print("- Registro:", "0x{:04X}".format(config.SENSOR_REGISTER_ADDR))
    print("- Valor exemplo: 235 (para 23.5°C)")

def test_configuration():
    """
    Verifica se as configurações estão corretas
    """
    print("\n=== VERIFICAÇÃO DE CONFIGURAÇÃO ===")
    
    print("Configurações do Slave:")
    print("- Endereço:", config.SLAVE_ADDR)
    print("- UART:", config.SLAVE_UART_ID)
    print("- Pinos:", config.SLAVE_PINS)
    print("- Controle DE/RE:", config.SLAVE_CTRL_PIN)
    print("- Registro:", "0x{:04X}".format(config.SLAVE_REGISTER_ADDR))
    
    print("\nConfigurações do Master:")
    print("- UART:", config.MASTER_UART_ID)
    print("- Pinos:", config.MASTER_PINS)
    print("- Sensor endereço:", config.SENSOR_SLAVE_ADDR)
    print("- Sensor registro:", "0x{:04X}".format(config.SENSOR_REGISTER_ADDR))
    
    print("\nConfigurações de Comunicação:")
    print("- Baudrate:", config.BAUDRATE)
    print("- Data bits:", config.DATA_BITS)
    print("- Stop bits:", config.STOP_BITS)
    print("- Parity:", config.PARITY)
    
    print("\nConfigurações do Sistema:")
    print("- Intervalo de leitura:", config.SENSOR_READ_INTERVAL, "ms")
    print("- Fator de escala:", config.TEMP_SCALE_FACTOR)
    print("- Debug habilitado:", config.DEBUG_ENABLED)

def main():
    """
    Executa todos os testes
    """
    print("TESTE DO SISTEMA MODBUS MASTER/SLAVE")
    print("=" * 40)
    
    test_configuration()
    test_sensor_simulation()
    
    # Aguarda um pouco antes de testar comunicação
    print("\nAguardando 2 segundos antes do teste de comunicação...")
    time.sleep(2)
    
    # Teste de comunicação (comentado por padrão para evitar conflitos)
    # test_as_master()
    
    print("\n=== INSTRUÇÕES PARA TESTE COMPLETO ===")
    print("1. Carregue o main.py no Pico")
    print("2. Conecte um sensor Modbus no endereço", config.SENSOR_SLAVE_ADDR)
    print("3. Use um master Modbus para ler do endereço", config.SLAVE_ADDR)
    print("4. Verifique se a temperatura é atualizada corretamente")
    print("5. Monitore o display OLED para status visual")

if __name__ == "__main__":
    main()
