# Configurações alternativas para testar diferentes cenários

# === CENÁRIO 1: Configuração padrão ===
CENARIO_1 = {
    "SENSOR_SLAVE_ADDR": 1,
    "SENSOR_REGISTER_ADDR": 0x0000,
    "TEMP_SCALE_FACTOR": 10,
    "BAUDRATE": 9600,
    "PARITY": None,
    "DESCRIPTION": "Configuração padrão - Holding register 0x0000, escala /10"
}

# === CENÁRIO 2: Registro 0x0200 (sua configuração atual) ===
CENARIO_2 = {
    "SENSOR_SLAVE_ADDR": 1,
    "SENSOR_REGISTER_ADDR": 0x0200,
    "TEMP_SCALE_FACTOR": 10,
    "BAUDRATE": 9600,
    "PARITY": None,
    "DESCRIPTION": "Registro 0x0200, escala /10"
}

# === CENÁRIO 3: Sem escala (valor direto) ===
CENARIO_3 = {
    "SENSOR_SLAVE_ADDR": 1,
    "SENSOR_REGISTER_ADDR": 0x0000,
    "TEMP_SCALE_FACTOR": 1,
    "BAUDRATE": 9600,
    "PARITY": None,
    "DESCRIPTION": "Valor direto sem escala"
}

# === CENÁRIO 4: Escala /100 ===
CENARIO_4 = {
    "SENSOR_SLAVE_ADDR": 1,
    "SENSOR_REGISTER_ADDR": 0x0000,
    "TEMP_SCALE_FACTOR": 100,
    "BAUDRATE": 9600,
    "PARITY": None,
    "DESCRIPTION": "Escala /100 para sensores de alta precisão"
}

# === CENÁRIO 5: Endereço slave diferente ===
CENARIO_5 = {
    "SENSOR_SLAVE_ADDR": 2,
    "SENSOR_REGISTER_ADDR": 0x0000,
    "TEMP_SCALE_FACTOR": 10,
    "BAUDRATE": 9600,
    "PARITY": None,
    "DESCRIPTION": "Slave endereço 2"
}

# === CENÁRIO 6: Baudrate diferente ===
CENARIO_6 = {
    "SENSOR_SLAVE_ADDR": 1,
    "SENSOR_REGISTER_ADDR": 0x0000,
    "TEMP_SCALE_FACTOR": 10,
    "BAUDRATE": 19200,
    "PARITY": None,
    "DESCRIPTION": "Baudrate 19200"
}

# === CENÁRIO 7: Com paridade ===
CENARIO_7 = {
    "SENSOR_SLAVE_ADDR": 1,
    "SENSOR_REGISTER_ADDR": 0x0000,
    "TEMP_SCALE_FACTOR": 10,
    "BAUDRATE": 9600,
    "PARITY": 0,  # Even parity
    "DESCRIPTION": "Com paridade par"
}

# === CENÁRIO 8: Registro típico de sensores industriais ===
CENARIO_8 = {
    "SENSOR_SLAVE_ADDR": 1,
    "SENSOR_REGISTER_ADDR": 0x0001,
    "TEMP_SCALE_FACTOR": 10,
    "BAUDRATE": 9600,
    "PARITY": None,
    "DESCRIPTION": "Registro 0x0001 (comum em sensores industriais)"
}

# Lista de todos os cenários para teste
CENARIOS = [
    CENARIO_1, CENARIO_2, CENARIO_3, CENARIO_4,
    CENARIO_5, CENARIO_6, CENARIO_7, CENARIO_8
]

# === INSTRUÇÕES DE USO ===
"""
Para testar um cenário específico:

1. Copie as configurações do cenário desejado para config.py
2. Ou modifique o teste para usar diretamente

Exemplo para usar CENARIO_2:
    SENSOR_SLAVE_ADDR = 1
    SENSOR_REGISTER_ADDR = 0x0200
    TEMP_SCALE_FACTOR = 10
    BAUDRATE = 9600
    PARITY = None

Sinais de que encontrou a configuração correta:
- Valor raw diferente de 0
- Temperatura calculada próxima do valor real (24.8°C)
- Comunicação estável sem erros
"""
